package com.ejuetc.commons.base.usertype.functions;

import org.hibernate.boot.model.FunctionContributions;
import org.hibernate.boot.model.FunctionContributor;

/**
 * 自定义Hibernate函数贡献者
 * 注册自定义SQL函数以支持ListUT在HQL查询中的使用
 * 
 * <AUTHOR>
 */
public class CustomFunctionsContributor implements FunctionContributor {

    @Override
    public void contributeFunctions(FunctionContributions functionContributions) {
        // 注册字符串包含函数，用于检查逗号分隔的字符串是否包含指定值
        functionContributions.getFunctionRegistry()
                .register("string_contains", new StringContainsSQLFunction("string_contains"));
    }
}
