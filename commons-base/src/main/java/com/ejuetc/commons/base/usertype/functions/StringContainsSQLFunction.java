package com.ejuetc.commons.base.usertype.functions;

import org.hibernate.dialect.function.StandardSQLFunction;
import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.type.BasicTypeReference;
import org.hibernate.type.SqlTypes;

import java.util.List;

/**
 * 自定义SQL函数：检查逗号分隔的字符串是否包含指定值
 * 用于解决ListUT在HQL查询中无法使用member of操作符的问题
 * 
 * <AUTHOR>
 */
class StringContainsSQLFunction extends StandardSQLFunction {

    private static final BasicTypeReference<Boolean> RETURN_TYPE = 
        new BasicTypeReference<>("boolean", Boolean.class, SqlTypes.BOOLEAN);

    StringContainsSQLFunction(final String functionName) {
        super(functionName, true, RETURN_TYPE);
    }

    @Override
    public void render(SqlAppender sqlAppender, List<? extends SqlAstNode> arguments, SqlAstTranslator<?> translator) {
        if (arguments.size() != 2) {
            throw new IllegalArgumentException(String.format("Function '%s' requires exactly 2 arguments", getName()));
        }

        // 生成SQL: FIND_IN_SET(value, comma_separated_string) > 0
        // 这个函数在MySQL中可以检查逗号分隔字符串中是否包含指定值
        sqlAppender.append("(FIND_IN_SET(");
        arguments.get(1).accept(translator); // 要查找的值
        sqlAppender.append(", ");
        arguments.get(0).accept(translator); // 逗号分隔的字符串
        sqlAppender.append(") > 0)");
    }
}
